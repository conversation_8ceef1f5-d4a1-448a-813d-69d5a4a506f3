// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },
  
  // Modules
  modules: ['@nuxt/ui', '@pinia/nuxt'],

  // Disable Nuxt UI fonts module to prevent Google Fonts fetching
  ui: {
    fonts: false
  },
  
  // TypeScript configuration
  typescript: {
    strict: true,
    typeCheck: true
  },
  
  // CSS configuration
  css: [],
  
  // Runtime config for environment variables
  runtimeConfig: {
    // Private keys (only available on server-side)
    jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret-key',
    dbHost: process.env.DB_HOST || 'localhost',
    dbPort: process.env.DB_PORT || '3306',
    dbUser: process.env.DB_USER || 'root',
    dbPassword: process.env.DB_PASSWORD || '',
    dbName: process.env.DB_NAME || 'student_task_management',
    
    // Public keys (exposed to client-side)
    public: {
      apiBase: '/api'
    }
  },
  
  // Server-side rendering configuration
  ssr: true,
  
  // App configuration
  app: {
    head: {
      title: 'Student Task Management System',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: 'A comprehensive task management system for students and teachers' }
      ]
    }
  },
  
  // Nitro configuration for server
  nitro: {
    experimental: {
      wasm: true
    }
  }
})
