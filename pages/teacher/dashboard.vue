<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">
          教师仪表盘
        </h1>
        <p class="text-gray-600 mt-2">
          欢迎回来，{{ user?.full_name || user?.username }}
        </p>
      </div>
      <UButton @click="handleLogout" variant="outline">
        登出
      </UButton>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">项目统计</h3>
        </template>
        <div class="text-center">
          <div class="text-3xl font-bold text-blue-600">0</div>
          <div class="text-sm text-gray-500">活跃项目</div>
        </div>
      </UCard>

      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">学生统计</h3>
        </template>
        <div class="text-center">
          <div class="text-3xl font-bold text-green-600">0</div>
          <div class="text-sm text-gray-500">参与学生</div>
        </div>
      </UCard>

      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">待审阅</h3>
        </template>
        <div class="text-center">
          <div class="text-3xl font-bold text-orange-600">0</div>
          <div class="text-sm text-gray-500">提交记录</div>
        </div>
      </UCard>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">最近提交</h3>
        </template>
        <div class="text-center py-8 text-gray-500">
          暂无提交记录
        </div>
      </UCard>

      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">快速操作</h3>
        </template>
        <div class="space-y-3">
          <UButton class="w-full" variant="outline">
            创建新项目
          </UButton>
          <UButton class="w-full" variant="outline">
            发布任务
          </UButton>
          <UButton class="w-full" variant="outline">
            查看学生进度
          </UButton>
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// Auth store
const authStore = useAuthStore()
const user = computed(() => authStore.user)

// Handle logout
const handleLogout = async () => {
  await authStore.logout()
}

// Set page title
useHead({
  title: '教师仪表盘 - 学生任务管理系统'
})
</script>
