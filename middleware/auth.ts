// Authentication middleware
export default defineNuxtRouteMiddleware(async (to, _from) => {
  // Skip authentication for login page
  if (to.path === '/login') {
    return
  }

  // Get auth store
  const { $fetch } = useNuxtApp()

  try {
    // Try to get current user from server
    const response = await $fetch('/api/auth/me', {
      headers: {
        'Authorization': `Bearer ${useCookie('accessToken').value || ''}`
      }
    })

    if (!response.success || !response.user) {
      // Try to refresh token
      try {
        const refreshResponse = await $fetch('/api/auth/refresh', {
          method: 'POST'
        })

        if (refreshResponse.success && refreshResponse.user) {
          // Set new access token
          const accessTokenCookie = useCookie('accessToken', {
            maxAge: 24 * 60 * 60, // 24 hours
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict'
          })
          accessTokenCookie.value = refreshResponse.token
          return
        }
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError)
      }

      // Redirect to login if authentication fails
      return navigateTo('/login')
    }
  } catch (error) {
    console.error('Authentication check failed:', error)
    return navigateTo('/login')
  }
})