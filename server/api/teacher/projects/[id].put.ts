import { requireTeacher } from '../../../../utils/auth-server'
import { ProjectRepository } from '../../../../repositories/project'
import { UserRepository } from '../../../../repositories/user'
import { z } from 'zod'

// 更新项目请求验证
const updateProjectSchema = z.object({
  project_name: z.string().min(1, '项目名称不能为空').max(100, '项目名称不能超过100个字符').optional(),
  description: z.string().optional(),
  student_ids: z.array(z.number()).optional()
})

// 更新项目响应接口
interface UpdateProjectResponse {
  success: boolean
  data?: {
    project_id: number
    project_name: string
    description: string | null
    created_at: string
  }
  error?: {
    code: string
    message: string
  }
}

export default defineEventHandler(async (event): Promise<UpdateProjectResponse> => {
  try {
    // 验证教师权限
    const teacher = await requireTeacher(event)

    // 获取项目ID
    const projectId = parseInt(getRouterParam(event, 'id') || '0')
    if (!projectId) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'INVALID_PROJECT_ID',
          message: '无效的项目ID'
        }
      }
    }

    // 获取请求体
    const body = await readBody(event)

    // 验证请求数据
    const validation = updateProjectSchema.safeParse(body)
    if (!validation.success) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: validation.error.errors[0]?.message || '请求数据格式错误'
        }
      }
    }

    const { project_name, description, student_ids } = validation.data

    // 初始化仓储
    const projectRepository = new ProjectRepository()
    const userRepository = new UserRepository()

    // 获取项目详情并验证权限
    const project = await projectRepository.findById(projectId)
    if (!project) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'PROJECT_NOT_FOUND',
          message: '项目不存在'
        }
      }
    }

    if (project.teacher_id !== teacher.user_id) {
      setResponseStatus(event, 403)
      return {
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: '无权修改此项目'
        }
      }
    }

    // 验证学生ID是否有效
    if (student_ids && student_ids.length > 0) {
      for (const studentId of student_ids) {
        const student = await userRepository.findById(studentId)
        if (!student || student.role !== 'student') {
          setResponseStatus(event, 400)
          return {
            success: false,
            error: {
              code: 'INVALID_STUDENT',
              message: `学生ID ${studentId} 无效`
            }
          }
        }
      }
    }

    // 更新项目基本信息
    const updateData: any = {}
    if (project_name !== undefined) updateData.project_name = project_name
    if (description !== undefined) updateData.description = description

    let updatedProject = project
    if (Object.keys(updateData).length > 0) {
      updatedProject = await projectRepository.updateProject(projectId, updateData)
      if (!updatedProject) {
        setResponseStatus(event, 500)
        return {
          success: false,
          error: {
            code: 'UPDATE_FAILED',
            message: '更新项目失败'
          }
        }
      }
    }

    // 更新学生分配
    if (student_ids !== undefined) {
      await projectRepository.assignStudentsToProject(projectId, student_ids)
    }

    return {
      success: true,
      data: {
        project_id: updatedProject.project_id,
        project_name: updatedProject.project_name,
        description: updatedProject.description,
        created_at: updatedProject.created_at.toISOString()
      }
    }
  } catch (error) {
    console.error('Update project API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '更新项目时发生错误'
      }
    }
  }
})
