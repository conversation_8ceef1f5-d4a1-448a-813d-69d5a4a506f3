import { BaseRepository } from './base'
import { safeExecuteQuery, safeExecuteQuerySingle, safeExecuteTransaction } from '../utils/database'

// 反馈数据模型
export interface Feedback {
  feedback_id: number
  submission_id: number
  teacher_id: number
  comment: string
  created_at: Date
}

// 创建反馈数据
export interface CreateFeedbackData {
  submission_id: number
  teacher_id: number
  comment: string
}

// 更新反馈数据
export interface UpdateFeedbackData {
  comment?: string
}

// 反馈详情（包含提交和学生信息）
export interface FeedbackWithDetails extends Feedback {
  task_title: string
  project_name: string
  student_name: string
  student_username: string
  teacher_name: string
  submission_status: string
  submission_date: Date
  progress_description: string
}

// 反馈统计信息
export interface FeedbackStats {
  total_feedback: number
  recent_feedback: number
  feedback_by_teacher: Array<{
    teacher_id: number
    teacher_name: string
    feedback_count: number
  }>
}

// 反馈仓储类
export class FeedbackRepository extends BaseRepository<Feedback> {
  protected tableName = 'feedback'
  protected primaryKey = 'feedback_id'

  // 创建反馈
  async createFeedback(feedbackData: CreateFeedbackData): Promise<Feedback> {
    const dataToCreate = {
      submission_id: feedbackData.submission_id,
      teacher_id: feedbackData.teacher_id,
      comment: feedbackData.comment
    }

    return this.create(dataToCreate)
  }

  // 创建反馈并更新提交状态
  async createFeedbackWithStatus(
    feedbackData: CreateFeedbackData, 
    submissionStatus: 'completed' | 'needs_revision'
  ): Promise<Feedback> {
    return safeExecuteTransaction(async (connection) => {
      // 创建反馈
      const feedback = await this.createInTransaction(connection, {
        submission_id: feedbackData.submission_id,
        teacher_id: feedbackData.teacher_id,
        comment: feedbackData.comment
      })

      // 更新提交状态
      await connection.execute(
        'UPDATE submissions SET status = ? WHERE submission_id = ?',
        [submissionStatus, feedbackData.submission_id]
      )

      return feedback
    })
  }

  // 更新反馈
  async updateFeedback(feedbackId: number, feedbackData: UpdateFeedbackData): Promise<Feedback | null> {
    const updateData: Partial<Feedback> = {}

    if (feedbackData.comment !== undefined) {
      updateData.comment = feedbackData.comment
    }

    return this.update(feedbackId, updateData)
  }

  // 根据提交ID查找反馈
  async findBySubmissionId(submissionId: number): Promise<FeedbackWithDetails[]> {
    const sql = `
      SELECT 
        f.*,
        t.title as task_title,
        p.project_name,
        us.full_name as student_name,
        us.username as student_username,
        ut.full_name as teacher_name,
        s.status as submission_status,
        s.submission_date,
        s.progress_description
      FROM feedback f
      JOIN submissions s ON f.submission_id = s.submission_id
      JOIN tasks ta ON s.task_id = ta.task_id
      JOIN projects p ON ta.project_id = p.project_id
      JOIN users us ON s.student_id = us.user_id
      JOIN users ut ON f.teacher_id = ut.user_id
      WHERE f.submission_id = ?
      ORDER BY f.created_at DESC
    `
    return safeExecuteQuery<FeedbackWithDetails>(sql, [submissionId])
  }

  // 根据学生ID查找反馈
  async findByStudentId(studentId: number): Promise<FeedbackWithDetails[]> {
    const sql = `
      SELECT 
        f.*,
        t.title as task_title,
        p.project_name,
        us.full_name as student_name,
        us.username as student_username,
        ut.full_name as teacher_name,
        s.status as submission_status,
        s.submission_date,
        s.progress_description
      FROM feedback f
      JOIN submissions s ON f.submission_id = s.submission_id
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users us ON s.student_id = us.user_id
      JOIN users ut ON f.teacher_id = ut.user_id
      WHERE s.student_id = ?
      ORDER BY f.created_at DESC
    `
    return safeExecuteQuery<FeedbackWithDetails>(sql, [studentId])
  }

  // 根据教师ID查找反馈
  async findByTeacherId(teacherId: number): Promise<FeedbackWithDetails[]> {
    const sql = `
      SELECT 
        f.*,
        t.title as task_title,
        p.project_name,
        us.full_name as student_name,
        us.username as student_username,
        ut.full_name as teacher_name,
        s.status as submission_status,
        s.submission_date,
        s.progress_description
      FROM feedback f
      JOIN submissions s ON f.submission_id = s.submission_id
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users us ON s.student_id = us.user_id
      JOIN users ut ON f.teacher_id = ut.user_id
      WHERE f.teacher_id = ?
      ORDER BY f.created_at DESC
    `
    return safeExecuteQuery<FeedbackWithDetails>(sql, [teacherId])
  }

  // 获取最近的反馈
  async findRecentFeedback(limit: number = 10): Promise<FeedbackWithDetails[]> {
    const sql = `
      SELECT 
        f.*,
        t.title as task_title,
        p.project_name,
        us.full_name as student_name,
        us.username as student_username,
        ut.full_name as teacher_name,
        s.status as submission_status,
        s.submission_date,
        s.progress_description
      FROM feedback f
      JOIN submissions s ON f.submission_id = s.submission_id
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users us ON s.student_id = us.user_id
      JOIN users ut ON f.teacher_id = ut.user_id
      ORDER BY f.created_at DESC
      LIMIT ?
    `
    return safeExecuteQuery<FeedbackWithDetails>(sql, [limit])
  }

  // 获取学生最近收到的反馈
  async findRecentByStudentId(studentId: number, limit: number = 5): Promise<FeedbackWithDetails[]> {
    const sql = `
      SELECT 
        f.*,
        t.title as task_title,
        p.project_name,
        us.full_name as student_name,
        us.username as student_username,
        ut.full_name as teacher_name,
        s.status as submission_status,
        s.submission_date,
        s.progress_description
      FROM feedback f
      JOIN submissions s ON f.submission_id = s.submission_id
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users us ON s.student_id = us.user_id
      JOIN users ut ON f.teacher_id = ut.user_id
      WHERE s.student_id = ?
      ORDER BY f.created_at DESC
      LIMIT ?
    `
    return safeExecuteQuery<FeedbackWithDetails>(sql, [studentId, limit])
  }

  // 获取教师最近给出的反馈
  async findRecentByTeacherId(teacherId: number, limit: number = 10): Promise<FeedbackWithDetails[]> {
    const sql = `
      SELECT 
        f.*,
        t.title as task_title,
        p.project_name,
        us.full_name as student_name,
        us.username as student_username,
        ut.full_name as teacher_name,
        s.status as submission_status,
        s.submission_date,
        s.progress_description
      FROM feedback f
      JOIN submissions s ON f.submission_id = s.submission_id
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users us ON s.student_id = us.user_id
      JOIN users ut ON f.teacher_id = ut.user_id
      WHERE f.teacher_id = ?
      ORDER BY f.created_at DESC
      LIMIT ?
    `
    return safeExecuteQuery<FeedbackWithDetails>(sql, [teacherId, limit])
  }

  // 搜索反馈
  async searchFeedback(query: string, teacherId?: number, studentId?: number): Promise<FeedbackWithDetails[]> {
    let sql = `
      SELECT 
        f.*,
        t.title as task_title,
        p.project_name,
        us.full_name as student_name,
        us.username as student_username,
        ut.full_name as teacher_name,
        s.status as submission_status,
        s.submission_date,
        s.progress_description
      FROM feedback f
      JOIN submissions s ON f.submission_id = s.submission_id
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users us ON s.student_id = us.user_id
      JOIN users ut ON f.teacher_id = ut.user_id
      WHERE (f.comment LIKE ? OR t.title LIKE ? OR us.full_name LIKE ?)
    `
    const params = [`%${query}%`, `%${query}%`, `%${query}%`]

    if (teacherId) {
      sql += ' AND f.teacher_id = ?'
      params.push(String(teacherId))
    }

    if (studentId) {
      sql += ' AND s.student_id = ?'
      params.push(String(studentId))
    }

    sql += ' ORDER BY f.created_at DESC LIMIT 50'

    return safeExecuteQuery<FeedbackWithDetails>(sql, params)
  }

  // 获取反馈统计信息
  async getFeedbackStats(): Promise<FeedbackStats> {
    const totalSql = 'SELECT COUNT(*) as total_feedback FROM feedback'
    const recentSql = 'SELECT COUNT(*) as recent_feedback FROM feedback WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)'
    const teacherSql = `
      SELECT 
        f.teacher_id,
        u.full_name as teacher_name,
        COUNT(*) as feedback_count
      FROM feedback f
      JOIN users u ON f.teacher_id = u.user_id
      GROUP BY f.teacher_id, u.full_name
      ORDER BY feedback_count DESC
      LIMIT 10
    `

    const [totalResult, recentResult, teacherResults] = await Promise.all([
      safeExecuteQuerySingle<{ total_feedback: number }>(totalSql),
      safeExecuteQuerySingle<{ recent_feedback: number }>(recentSql),
      safeExecuteQuery<{ teacher_id: number; teacher_name: string; feedback_count: number }>(teacherSql)
    ])

    return {
      total_feedback: totalResult?.total_feedback || 0,
      recent_feedback: recentResult?.recent_feedback || 0,
      feedback_by_teacher: teacherResults || []
    }
  }

  // 获取任务的反馈统计
  async getFeedbackStatsByTaskId(taskId: number): Promise<{
    total_feedback: number
    positive_feedback: number
    revision_feedback: number
  }> {
    const sql = `
      SELECT 
        COUNT(*) as total_feedback,
        COUNT(CASE WHEN s.status = 'completed' THEN 1 END) as positive_feedback,
        COUNT(CASE WHEN s.status = 'needs_revision' THEN 1 END) as revision_feedback
      FROM feedback f
      JOIN submissions s ON f.submission_id = s.submission_id
      WHERE s.task_id = ?
    `
    
    const result = await safeExecuteQuerySingle<{
      total_feedback: number
      positive_feedback: number
      revision_feedback: number
    }>(sql, [taskId])
    
    return result || {
      total_feedback: 0,
      positive_feedback: 0,
      revision_feedback: 0
    }
  }

  // 检查提交是否有反馈
  async hasSubmissionFeedback(submissionId: number): Promise<boolean> {
    const sql = 'SELECT 1 FROM feedback WHERE submission_id = ? LIMIT 1'
    const result = await safeExecuteQuerySingle(sql, [submissionId])
    return result !== null
  }

  // 获取反馈详情（包含完整信息）
  async findByIdWithDetails(feedbackId: number): Promise<FeedbackWithDetails | null> {
    const sql = `
      SELECT 
        f.*,
        t.title as task_title,
        p.project_name,
        us.full_name as student_name,
        us.username as student_username,
        ut.full_name as teacher_name,
        s.status as submission_status,
        s.submission_date,
        s.progress_description
      FROM feedback f
      JOIN submissions s ON f.submission_id = s.submission_id
      JOIN tasks t ON s.task_id = t.task_id
      JOIN projects p ON t.project_id = p.project_id
      JOIN users us ON s.student_id = us.user_id
      JOIN users ut ON f.teacher_id = ut.user_id
      WHERE f.feedback_id = ?
      LIMIT 1
    `
    return safeExecuteQuerySingle<FeedbackWithDetails>(sql, [feedbackId])
  }

  // 删除反馈
  async deleteFeedback(feedbackId: number): Promise<boolean> {
    return this.delete(feedbackId)
  }
}
