import { BaseRepository } from './base'
import { safeExecuteQuery, safeExecuteQuerySingle } from '../utils/database'

// 任务数据模型
export interface Task {
  task_id: number
  project_id: number
  title: string
  description: string | null
  due_date: Date | null
  created_at: Date
}

// 创建任务数据
export interface CreateTaskData {
  project_id: number
  title: string
  description?: string
  due_date?: Date | string
}

// 更新任务数据
export interface UpdateTaskData {
  title?: string
  description?: string
  due_date?: Date | string | null
}

// 任务详情（包含项目信息）
export interface TaskWithProject extends Task {
  project_name: string
  teacher_id: number
  teacher_name: string
}

// 任务统计信息
export interface TaskStats {
  task_id: number
  title: string
  total_students: number
  submitted_count: number
  completed_count: number
  needs_revision_count: number
}

// 学生任务视图
export interface StudentTaskView extends Task {
  project_name: string
  teacher_name: string
  submission_status: 'not_submitted' | 'in_progress' | 'submitted' | 'completed' | 'needs_revision'
  latest_submission_id: number | null
  latest_submission_date: Date | null
}

// 任务仓储类
export class TaskRepository extends BaseRepository<Task> {
  protected tableName = 'tasks'
  protected primaryKey = 'task_id'

  // 根据项目ID查找任务
  async findByProjectId(projectId: number): Promise<Task[]> {
    const sql = 'SELECT * FROM tasks WHERE project_id = ? ORDER BY due_date ASC, created_at DESC'
    return safeExecuteQuery<Task>(sql, [projectId])
  }

  // 创建任务
  async createTask(taskData: CreateTaskData): Promise<Task> {
    const dataToCreate = {
      project_id: taskData.project_id,
      title: taskData.title,
      description: taskData.description || null,
      due_date: taskData.due_date ? new Date(taskData.due_date) : null
    }

    return this.create(dataToCreate)
  }

  // 更新任务
  async updateTask(taskId: number, taskData: UpdateTaskData): Promise<Task | null> {
    const updateData: Partial<Task> = {}

    if (taskData.title !== undefined) {
      updateData.title = taskData.title
    }

    if (taskData.description !== undefined) {
      updateData.description = taskData.description
    }

    if (taskData.due_date !== undefined) {
      updateData.due_date = taskData.due_date ? new Date(taskData.due_date) : null
    }

    return this.update(taskId, updateData)
  }

  // 获取任务详情（包含项目信息）
  async findByIdWithProject(taskId: number): Promise<TaskWithProject | null> {
    const sql = `
      SELECT 
        t.*,
        p.project_name,
        p.teacher_id,
        u.full_name as teacher_name
      FROM tasks t
      JOIN projects p ON t.project_id = p.project_id
      JOIN users u ON p.teacher_id = u.user_id
      WHERE t.task_id = ?
      LIMIT 1
    `
    return safeExecuteQuerySingle<TaskWithProject>(sql, [taskId])
  }

  // 获取学生的任务列表（包含提交状态）
  async findByStudentId(studentId: number): Promise<StudentTaskView[]> {
    const sql = `
      SELECT 
        t.*,
        p.project_name,
        u.full_name as teacher_name,
        COALESCE(
          (SELECT status 
           FROM submissions s 
           WHERE s.task_id = t.task_id AND s.student_id = ? 
           ORDER BY s.submission_date DESC 
           LIMIT 1), 
          'not_submitted'
        ) as submission_status,
        (SELECT submission_id 
         FROM submissions s 
         WHERE s.task_id = t.task_id AND s.student_id = ? 
         ORDER BY s.submission_date DESC 
         LIMIT 1) as latest_submission_id,
        (SELECT submission_date 
         FROM submissions s 
         WHERE s.task_id = t.task_id AND s.student_id = ? 
         ORDER BY s.submission_date DESC 
         LIMIT 1) as latest_submission_date
      FROM tasks t
      JOIN projects p ON t.project_id = p.project_id
      JOIN users u ON p.teacher_id = u.user_id
      JOIN student_projects sp ON p.project_id = sp.project_id
      WHERE sp.student_id = ?
      ORDER BY 
        CASE 
          WHEN t.due_date IS NULL THEN 1 
          ELSE 0 
        END,
        t.due_date ASC,
        t.created_at DESC
    `
    return safeExecuteQuery<StudentTaskView>(sql, [studentId, studentId, studentId, studentId])
  }

  // 获取教师的任务列表（包含统计信息）
  async findByTeacherIdWithStats(teacherId: number): Promise<TaskStats[]> {
    const sql = `
      SELECT 
        t.task_id,
        t.title,
        COUNT(DISTINCT sp.student_id) as total_students,
        COUNT(DISTINCT CASE WHEN s.status IN ('submitted', 'completed', 'needs_revision') THEN s.student_id END) as submitted_count,
        COUNT(DISTINCT CASE WHEN s.status = 'completed' THEN s.student_id END) as completed_count,
        COUNT(DISTINCT CASE WHEN s.status = 'needs_revision' THEN s.student_id END) as needs_revision_count
      FROM tasks t
      JOIN projects p ON t.project_id = p.project_id
      LEFT JOIN student_projects sp ON p.project_id = sp.project_id
      LEFT JOIN submissions s ON t.task_id = s.task_id AND s.student_id = sp.student_id
      WHERE p.teacher_id = ?
      GROUP BY t.task_id, t.title
      ORDER BY t.created_at DESC
    `
    return safeExecuteQuery<TaskStats>(sql, [teacherId])
  }

  // 获取即将到期的任务
  async findUpcomingTasks(studentId: number, days: number = 7): Promise<StudentTaskView[]> {
    const sql = `
      SELECT 
        t.*,
        p.project_name,
        u.full_name as teacher_name,
        COALESCE(
          (SELECT status 
           FROM submissions s 
           WHERE s.task_id = t.task_id AND s.student_id = ? 
           ORDER BY s.submission_date DESC 
           LIMIT 1), 
          'not_submitted'
        ) as submission_status,
        (SELECT submission_id 
         FROM submissions s 
         WHERE s.task_id = t.task_id AND s.student_id = ? 
         ORDER BY s.submission_date DESC 
         LIMIT 1) as latest_submission_id,
        (SELECT submission_date 
         FROM submissions s 
         WHERE s.task_id = t.task_id AND s.student_id = ? 
         ORDER BY s.submission_date DESC 
         LIMIT 1) as latest_submission_date
      FROM tasks t
      JOIN projects p ON t.project_id = p.project_id
      JOIN users u ON p.teacher_id = u.user_id
      JOIN student_projects sp ON p.project_id = sp.project_id
      WHERE sp.student_id = ?
        AND t.due_date IS NOT NULL
        AND t.due_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL ? DAY)
        AND COALESCE(
          (SELECT status 
           FROM submissions s 
           WHERE s.task_id = t.task_id AND s.student_id = ? 
           ORDER BY s.submission_date DESC 
           LIMIT 1), 
          'not_submitted'
        ) NOT IN ('completed')
      ORDER BY t.due_date ASC
    `
    return safeExecuteQuery<StudentTaskView>(sql, [studentId, studentId, studentId, studentId, days, studentId])
  }

  // 获取学生的进行中任务
  async findInProgressTasks(studentId: number): Promise<StudentTaskView[]> {
    const sql = `
      SELECT 
        t.*,
        p.project_name,
        u.full_name as teacher_name,
        s.status as submission_status,
        s.submission_id as latest_submission_id,
        s.submission_date as latest_submission_date
      FROM tasks t
      JOIN projects p ON t.project_id = p.project_id
      JOIN users u ON p.teacher_id = u.user_id
      JOIN student_projects sp ON p.project_id = sp.project_id
      JOIN submissions s ON t.task_id = s.task_id AND s.student_id = sp.student_id
      WHERE sp.student_id = ?
        AND s.status IN ('in_progress', 'needs_revision')
        AND s.submission_date = (
          SELECT MAX(submission_date) 
          FROM submissions s2 
          WHERE s2.task_id = t.task_id AND s2.student_id = ?
        )
      ORDER BY s.submission_date DESC
    `
    return safeExecuteQuery<StudentTaskView>(sql, [studentId, studentId])
  }

  // 搜索任务
  async searchTasks(query: string, projectId?: number, teacherId?: number): Promise<TaskWithProject[]> {
    let sql = `
      SELECT 
        t.*,
        p.project_name,
        p.teacher_id,
        u.full_name as teacher_name
      FROM tasks t
      JOIN projects p ON t.project_id = p.project_id
      JOIN users u ON p.teacher_id = u.user_id
      WHERE (t.title LIKE ? OR t.description LIKE ?)
    `
    const params = [`%${query}%`, `%${query}%`]

    if (projectId) {
      sql += ' AND t.project_id = ?'
      params.push(String(projectId))
    }

    if (teacherId) {
      sql += ' AND p.teacher_id = ?'
      params.push(String(teacherId))
    }

    sql += ' ORDER BY t.created_at DESC LIMIT 50'

    return safeExecuteQuery<TaskWithProject>(sql, params)
  }

  // 获取任务统计信息
  async getTaskStats(): Promise<{
    total_tasks: number
    active_tasks: number
    completed_tasks: number
    overdue_tasks: number
  }> {
    const sql = `
      SELECT 
        COUNT(DISTINCT t.task_id) as total_tasks,
        COUNT(DISTINCT CASE 
          WHEN EXISTS(
            SELECT 1 FROM submissions s 
            WHERE s.task_id = t.task_id 
            AND s.status IN ('in_progress', 'submitted', 'needs_revision')
          ) THEN t.task_id 
        END) as active_tasks,
        COUNT(DISTINCT CASE 
          WHEN EXISTS(
            SELECT 1 FROM submissions s 
            WHERE s.task_id = t.task_id 
            AND s.status = 'completed'
          ) THEN t.task_id 
        END) as completed_tasks,
        COUNT(DISTINCT CASE 
          WHEN t.due_date < NOW() 
          AND NOT EXISTS(
            SELECT 1 FROM submissions s 
            WHERE s.task_id = t.task_id 
            AND s.status = 'completed'
          ) THEN t.task_id 
        END) as overdue_tasks
      FROM tasks t
    `
    
    const result = await safeExecuteQuerySingle<{
      total_tasks: number
      active_tasks: number
      completed_tasks: number
      overdue_tasks: number
    }>(sql)
    
    return result || {
      total_tasks: 0,
      active_tasks: 0,
      completed_tasks: 0,
      overdue_tasks: 0
    }
  }

  // 检查任务是否属于指定项目
  async isTaskInProject(taskId: number, projectId: number): Promise<boolean> {
    const sql = 'SELECT 1 FROM tasks WHERE task_id = ? AND project_id = ? LIMIT 1'
    const result = await safeExecuteQuerySingle(sql, [taskId, projectId])
    return result !== null
  }

  // 检查学生是否可以访问任务
  async canStudentAccessTask(taskId: number, studentId: number): Promise<boolean> {
    const sql = `
      SELECT 1 
      FROM tasks t
      JOIN student_projects sp ON t.project_id = sp.project_id
      WHERE t.task_id = ? AND sp.student_id = ?
      LIMIT 1
    `
    const result = await safeExecuteQuerySingle(sql, [taskId, studentId])
    return result !== null
  }
}
